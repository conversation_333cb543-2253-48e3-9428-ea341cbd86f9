﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using VDS.RDF;
using FleetXQ.Data.DataObjects.Custom;
using DocumentFormat.OpenXml.Bibliography;


namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class ModuleUtilitiesTest : TestBase
    {
        private IModuleUtilities _moduleUtilities;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"ModuleUtilitiesTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _moduleUtilities = _serviceProvider.GetRequiredService<IModuleUtilities>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task GetAvailableModulesAsync_ReturnsCorrectModules()
        {
            // Arrange
            // Assuming the test setup has already created modules and vehicles
            // and some modules are not assigned to any vehicle.

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module.");
            foreach (var module in availableModules)
            {
                Assert.That(module.Vehicle, Is.Null, "Available module should not be assigned to any vehicle.");
            }
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithDealerId_ReturnsOnlyModulesForDealer()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module for dealer.");
            foreach (var module in availableModules)
            {
                Assert.That(module.DealerId, Is.EqualTo(dealer.Id), "Available module should belong to the specified dealer.");
                Assert.That(module.Status == ModuleStatusEnum.Spare || module.Status == null, "Available module should have Spare status or null status.");
            }
        }

        [Test]
        public async Task GetAvailableModulesAsync_OnlyReturnsSpareOrNullStatusModules()
        {
            // Arrange
            // Create a module with non-Spare status to ensure it's filtered out
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            var nonSpareModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            nonSpareModule.Id = Guid.NewGuid();
            nonSpareModule.Status = ModuleStatusEnum.Assigned;
            nonSpareModule.DealerId = dealer.Id;
            nonSpareModule.CCID = "TEST-CCID-NONSPARE";
            nonSpareModule.IoTDevice = "TEST-IOT-NONSPARE";
            nonSpareModule = await _dataFacade.ModuleDataProvider.SaveAsync(nonSpareModule);

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            foreach (var module in availableModules)
            {
                Assert.That(module.Status == ModuleStatusEnum.Spare || module.Status == null,
                    $"Module {module.Id} should have Spare status or null status, but has {module.Status}");
            }

            // Verify the non-Spare module is not in the results
            var nonSpareModuleInResults = availableModules.Any(m => m.Id == nonSpareModule.Id);
            Assert.That(nonSpareModuleInResults, Is.False, "Non-Spare module should not be in available modules list.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_ExcludesModulesAssignedToVehicles()
        {
            // Arrange
            // Get a vehicle that has a module assigned
            var vehicleWithModule = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null")).FirstOrDefault();
            Assert.That(vehicleWithModule, Is.Not.Null, "Test setup error: No vehicle with assigned module found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");

            // Verify the module assigned to the vehicle is not in the available modules
            var assignedModuleInResults = availableModules.Any(m => m.Id == vehicleWithModule.ModuleId1);
            Assert.That(assignedModuleInResults, Is.False, "Module assigned to vehicle should not be in available modules list.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithEmptyDealerId_ReturnsAllAvailableModules()
        {
            // Arrange
            // Get all modules that are not assigned to vehicles
            var allModules = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null);
            var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null");
            var assignedModuleIds = vehiclesWithModules.Select(v => v.ModuleId1).Distinct().ToArray();

            var expectedAvailableModules = allModules
                .Where(m => !assignedModuleIds.Contains(m.Id))
                .Where(m => m.Status == ModuleStatusEnum.Spare || m.Status == null)
                .ToList();

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Count, Is.EqualTo(expectedAvailableModules.Count),
                $"Expected {expectedAvailableModules.Count} available modules, but got {availableModules.Count}");
        }

        [Test]
        public async Task GetAvailableModulesAsync_WithNonExistentDealerId_ReturnsEmptyList()
        {
            // Arrange
            var nonExistentDealerId = Guid.NewGuid();

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(nonExistentDealerId);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "Response should not be null.");
            Assert.That(availableModules.Count, Is.EqualTo(0), "Should return empty list for non-existent dealer.");
        }

        [Test]
        public async Task GetAvailableModulesAsync_ReturnsCorrectModuleProperties()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act
            var response = await _moduleUtilities.GetAvailableModulesAsync(dealer.Id);
            var availableModules = response.Result.ToList();

            // Assert
            Assert.That(availableModules, Is.Not.Null, "No available modules returned.");
            Assert.That(availableModules.Any(), Is.True, "Expected at least one available module.");

            foreach (var module in availableModules)
            {
                // Verify essential properties are set
                Assert.That(module.Id, Is.Not.EqualTo(Guid.Empty), "Module should have a valid ID.");
                Assert.That(module.DealerId, Is.EqualTo(dealer.Id), "Module should belong to the specified dealer.");
                Assert.That(module.Status == ModuleStatusEnum.Spare || module.Status == null,
                    "Module should have Spare status or null status.");

                // Verify module is not assigned to any vehicle
                var vehicleWithThisModule = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 == @0", new object[] { module.Id })).FirstOrDefault();
                Assert.That(vehicleWithThisModule, Is.Null, $"Module {module.Id} should not be assigned to any vehicle.");
            }
        }

        [Test]
        public async Task ResetCalibrationAsync_ResetsModuleCalibration()
        {
            // Arrange
            var moduleToReset = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Calibration != 0")).FirstOrDefault();
            Assert.That(moduleToReset, Is.Not.Null, "Test setup error: No module with non-zero calibration found.");

            // Act
            var response = await _moduleUtilities.ResetCalibrationAsync(moduleToReset.Id);
            var resetModule = response.Result;

            // Assert
            Assert.That(resetModule.Calibration, Is.EqualTo(0), "Module calibration was not reset to 0.");
            Assert.That(resetModule.CalibrationResetDate, Is.LessThanOrEqualTo(DateTime.UtcNow), "Calibration reset date is not set correctly.");
            Assert.That(resetModule.BlueImpact, Is.EqualTo(0), "BlueImpact should be reset to 0.");
            Assert.That(resetModule.AmberImpact, Is.EqualTo(0), "AmberImpact should be reset to 0.");
            Assert.That(resetModule.RedImpact, Is.EqualTo(0), "RedImpact should be reset to 0.");
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Id = Guid.NewGuid();
            country.Name = "Australia";

            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Id = Guid.NewGuid();
            region.Name = "Victoria";
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Id = Guid.NewGuid();
            dealer.Name = "Test dealer";
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.Id = Guid.NewGuid();
            customer.CompanyName = "Test customer";
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.Id = Guid.NewGuid();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var sites = new List<string> { "Site 1", "Site 2", "Site 3" };
            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };

            foreach (var siteName in sites)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = Guid.NewGuid();
                site.CustomerId = customer.Id;
                site.Name = siteName;
                site.TimezoneId = timeZone.Id;

                site = await _dataFacade.SiteDataProvider.SaveAsync(site);

                var departmentNames = new List<string> { "Warehouse", "Logistics", "Production" };

                // create 3 departments for each site
                for (int j = 0; j < 3; j++)
                {
                    var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                    department.Id = Guid.NewGuid();
                    department.Name = siteName + " " + departmentNames[j];
                    department.SiteId = site.Id;
                    department.CustomerId = customer.Id; // Set the customer ID for the department
                    department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                    // get only 3 models
                    var Models = new List<ModelDataObject>();

                    for (int i = 0; i < 3; i++)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = "Model " + (i + 1).ToString();
                        model.Description = "Description for Model " + (i + 1).ToString();
                        model.DealerId = dealer.Id;
                        // Assigning Model.Type based on the ModelTypesEnum
                        switch (i)
                        {
                            case 0:
                                model.Type = ModelTypesEnum.Electric;
                                break;
                            case 1:
                                model.Type = ModelTypesEnum.ICForklifts;
                                break;
                            case 2:
                                model.Type = ModelTypesEnum.OrderPickers;
                                break;
                            // Additional cases can be added here for other types if necessary
                            default:
                                model.Type = ModelTypesEnum.PalletJack; // Default case if more than 3 models are created
                                break;
                        }
                        // Removed setting the non-existent Active property
                        model = await _dataFacade.ModelDataProvider.SaveAsync(model);
                        Models.Add(model);
                    }

                    // create 10 module for each department, 9 vechile, 1 spare module
                    for (int k = 0; k < 10; k++)
                    {
                        // create a module for the vehicle
                        var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                        module.Id = Guid.NewGuid();
                        module.Calibration = 100;
                        module.CCID = "CCID" + j + k;
                        // set FSSSBASE random from 100000 to 200000 in increment of 10000
                        Random random = new Random();
                        int randomNumber = random.Next(10, 21);
                        module.FSSSBase = randomNumber * 10000;
                        module.FSSXMulti = 1;
                        if (j == 0)
                            module.IoTDevice = IoTHubIds1[k] + department.Id;
                        else if (j == 1)
                            module.IoTDevice = IoTHubIds2[k] + department.Id;
                        else
                            module.IoTDevice = IoTHubIds3[k] + department.Id;
                        module.IsAllocatedToVehicle = true;
                        module.DealerId = dealer.Id; // Set the dealer ID for the module
                        module.Status = ModuleStatusEnum.Spare; // Set status to Spare
                        module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

                        if (k < 9) // only assigne 9 module to 9 vehcile, leave 1 module as spare
                        {
                            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                            vehicle.Id = Guid.NewGuid();
                            vehicle.CustomerId = customer.Id;
                            vehicle.SiteId = site.Id;
                            vehicle.DepartmentId = department.Id;
                            vehicle.IDLETimer = 300;
                            vehicle.OnHire = true;
                            vehicle.ImpactLockout = true;
                            // set random modelId with index rand 0 to 2
                            if (Models.Any())
                            {
                                vehicle.ModelId = Models[k % 3].Id;
                            }
                            else
                            {
                                throw new InvalidOperationException("No models found to assign to vehicle.");
                            }
                            if (j == 0)
                            {
                                vehicle.HireNo = VehicleHireNos1[k] + department.Id;
                                vehicle.SerialNo = VehicleSerialNos1[k] + department.Id;
                            }
                            else if (j == 1)
                            {
                                vehicle.HireNo = VehicleHireNos2[k] + department.Id;
                                vehicle.SerialNo = VehicleSerialNos2[k] + department.Id;
                            }
                            else
                            {
                                vehicle.HireNo = VehicleHireNos3[k] + department.Id;
                                vehicle.SerialNo = VehicleSerialNos3[k] + department.Id;
                            }

                            vehicle.ModuleId1 = module.Id;
                            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

                        }

                    }
                }
            }
        }
    }
}
