using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    /// <summary>
    /// Post-Optimization Performance Test Suite for Device ID Lookup Operations
    /// 
    /// This test suite uses the current (optimized) implementation of GetAvailableModulesAsync
    /// to measure improved performance metrics using identical test scenarios and measurement methodology
    /// as the pre-optimization tests.
    /// 
    /// Optimized Implementation Details:
    /// - Uses simplified query with direct Vehicle = null filter
    /// - Eliminates separate vehicle lookup step
    /// - Reduces query complexity and database round trips
    /// - Leverages database indexes for better performance
    /// </summary>
    [TestFixture]
    public class ModuleUtilitiesPostOptimizationPerformanceTest : TestBase
    {
        private IModuleUtilities _moduleUtilities;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"ModuleUtilitiesPostOptTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _moduleUtilities = _serviceProvider.GetRequiredService<IModuleUtilities>();

            CreateTestDatabase(_testDatabaseName);
            await CreateLargeTestDataSetAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        /// <summary>
        /// Performance measurement utility for consistent benchmarking
        /// </summary>
        public class PerformanceMeasurement
        {
            public TimeSpan ElapsedTime { get; set; }
            public int ResultCount { get; set; }
            public string TestScenario { get; set; }
            public Dictionary<string, object> AdditionalMetrics { get; set; } = new Dictionary<string, object>();
        }

        /// <summary>
        /// Measures the performance of a function execution
        /// </summary>
        private async Task<PerformanceMeasurement> MeasurePerformanceAsync<T>(
            Func<Task<T>> operation, 
            string scenario,
            Func<T, int> resultCounter = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = await operation();
            stopwatch.Stop();

            return new PerformanceMeasurement
            {
                ElapsedTime = stopwatch.Elapsed,
                ResultCount = resultCounter?.Invoke(result) ?? 0,
                TestScenario = scenario
            };
        }

        /// <summary>
        /// Runs multiple iterations of a performance test and returns average metrics
        /// </summary>
        private async Task<PerformanceMeasurement> RunPerformanceTestAsync<T>(
            Func<Task<T>> operation,
            string scenario,
            int iterations = 5,
            Func<T, int> resultCounter = null)
        {
            var measurements = new List<PerformanceMeasurement>();
            
            // Warm-up run
            await operation();
            
            // Actual measurements
            for (int i = 0; i < iterations; i++)
            {
                var measurement = await MeasurePerformanceAsync(operation, scenario, resultCounter);
                measurements.Add(measurement);
                
                // Small delay between iterations to avoid caching effects
                await Task.Delay(100);
            }

            var avgElapsed = TimeSpan.FromMilliseconds(measurements.Average(m => m.ElapsedTime.TotalMilliseconds));
            var avgResultCount = (int)measurements.Average(m => m.ResultCount);

            return new PerformanceMeasurement
            {
                ElapsedTime = avgElapsed,
                ResultCount = avgResultCount,
                TestScenario = scenario,
                AdditionalMetrics = new Dictionary<string, object>
                {
                    ["MinTime"] = measurements.Min(m => m.ElapsedTime),
                    ["MaxTime"] = measurements.Max(m => m.ElapsedTime),
                    ["Iterations"] = iterations,
                    ["StandardDeviation"] = CalculateStandardDeviation(measurements.Select(m => m.ElapsedTime.TotalMilliseconds))
                }
            };
        }

        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            var avg = values.Average();
            var sumOfSquares = values.Sum(v => Math.Pow(v - avg, 2));
            return Math.Sqrt(sumOfSquares / values.Count());
        }

        [Test]
        public async Task PostOptimization_SingleDeviceLookup_PerformanceImproved()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - Measure performance of single device lookup using optimized implementation
            var measurement = await RunPerformanceTestAsync(
                async () => await _moduleUtilities.GetAvailableModulesAsync(dealer.Id),
                "Post-Optimization Single Device Lookup",
                iterations: 10,
                resultCounter: result => result.Result.Count);

            // Assert
            Assert.That(measurement.ResultCount, Is.GreaterThan(0), "Should return available modules");
            
            // Log optimized performance metrics
            TestContext.WriteLine($"=== POST-OPTIMIZATION IMPROVED METRICS ===");
            TestContext.WriteLine($"Scenario: {measurement.TestScenario}");
            TestContext.WriteLine($"Average Execution Time: {measurement.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Min Time: {((TimeSpan)measurement.AdditionalMetrics["MinTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Max Time: {((TimeSpan)measurement.AdditionalMetrics["MaxTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Standard Deviation: {(double)measurement.AdditionalMetrics["StandardDeviation"]:F2} ms");
            TestContext.WriteLine($"Result Count: {measurement.ResultCount}");
            TestContext.WriteLine($"Iterations: {measurement.AdditionalMetrics["Iterations"]}");
        }

        [Test]
        public async Task PostOptimization_BatchDeviceLookup_PerformanceImproved()
        {
            // Arrange
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(null);
            var testDealers = dealers.Take(3).ToList();
            Assert.That(testDealers.Count, Is.GreaterThan(0), "Test setup error: No dealers found.");

            // Act - Measure performance of batch device lookups using optimized implementation
            var batchMeasurements = new List<PerformanceMeasurement>();
            
            foreach (var dealer in testDealers)
            {
                var measurement = await RunPerformanceTestAsync(
                    async () => await _moduleUtilities.GetAvailableModulesAsync(dealer.Id),
                    $"Post-Optimization Batch Lookup - Dealer {dealer.Name}",
                    iterations: 5,
                    resultCounter: result => result.Result.Count);
                
                batchMeasurements.Add(measurement);
            }

            // Assert and log batch performance
            var avgBatchTime = batchMeasurements.Average(m => m.ElapsedTime.TotalMilliseconds);
            var totalResults = batchMeasurements.Sum(m => m.ResultCount);

            TestContext.WriteLine($"=== POST-OPTIMIZATION BATCH IMPROVED METRICS ===");
            TestContext.WriteLine($"Dealers Tested: {testDealers.Count}");
            TestContext.WriteLine($"Average Batch Time: {avgBatchTime:F2} ms");
            TestContext.WriteLine($"Total Results: {totalResults}");
            
            foreach (var measurement in batchMeasurements)
            {
                TestContext.WriteLine($"  {measurement.TestScenario}: {measurement.ElapsedTime.TotalMilliseconds:F2} ms ({measurement.ResultCount} results)");
            }
        }

        [Test]
        public async Task PostOptimization_EmptyDealerLookup_PerformanceImproved()
        {
            // Act - Measure performance of lookup with empty dealer ID (all available modules)
            var measurement = await RunPerformanceTestAsync(
                async () => await _moduleUtilities.GetAvailableModulesAsync(Guid.Empty),
                "Post-Optimization Empty Dealer Lookup",
                iterations: 10,
                resultCounter: result => result.Result.Count);

            // Assert
            Assert.That(measurement.ResultCount, Is.GreaterThan(0), "Should return available modules");
            
            // Log performance metrics for empty dealer scenario
            TestContext.WriteLine($"=== POST-OPTIMIZATION EMPTY DEALER METRICS ===");
            TestContext.WriteLine($"Scenario: {measurement.TestScenario}");
            TestContext.WriteLine($"Average Execution Time: {measurement.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Min Time: {((TimeSpan)measurement.AdditionalMetrics["MinTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Max Time: {((TimeSpan)measurement.AdditionalMetrics["MaxTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Standard Deviation: {(double)measurement.AdditionalMetrics["StandardDeviation"]:F2} ms");
            TestContext.WriteLine($"Result Count: {measurement.ResultCount}");
            TestContext.WriteLine($"Iterations: {measurement.AdditionalMetrics["Iterations"]}");
        }

        [Test]
        public async Task PostOptimization_EdgeCase_NonExistentDealer_Performance()
        {
            // Arrange
            var nonExistentDealerId = Guid.NewGuid();

            // Act - Measure performance of lookup with non-existent dealer
            var measurement = await RunPerformanceTestAsync(
                async () => await _moduleUtilities.GetAvailableModulesAsync(nonExistentDealerId),
                "Post-Optimization Non-Existent Dealer Lookup",
                iterations: 10,
                resultCounter: result => result.Result.Count);

            // Assert
            Assert.That(measurement.ResultCount, Is.EqualTo(0), "Should return no modules for non-existent dealer");
            
            // Log performance metrics for edge case
            TestContext.WriteLine($"=== POST-OPTIMIZATION EDGE CASE METRICS ===");
            TestContext.WriteLine($"Scenario: {measurement.TestScenario}");
            TestContext.WriteLine($"Average Execution Time: {measurement.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Min Time: {((TimeSpan)measurement.AdditionalMetrics["MinTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Max Time: {((TimeSpan)measurement.AdditionalMetrics["MaxTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Standard Deviation: {(double)measurement.AdditionalMetrics["StandardDeviation"]:F2} ms");
            TestContext.WriteLine($"Result Count: {measurement.ResultCount}");
            TestContext.WriteLine($"Iterations: {measurement.AdditionalMetrics["Iterations"]}");
        }

        [Test]
        public async Task PostOptimization_VehicleCreationScenario_Performance()
        {
            // Arrange - Simulate the vehicle creation scenario where device lookup is critical
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(null);
            var testDealer = dealers.FirstOrDefault();
            Assert.That(testDealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - Simulate multiple rapid device lookups as would occur during vehicle creation
            var rapidLookupMeasurements = new List<PerformanceMeasurement>();
            
            for (int i = 0; i < 5; i++)
            {
                var measurement = await MeasurePerformanceAsync(
                    async () => await _moduleUtilities.GetAvailableModulesAsync(testDealer.Id),
                    $"Post-Optimization Vehicle Creation Simulation {i + 1}",
                    resultCounter: result => result.Result.Count);
                
                rapidLookupMeasurements.Add(measurement);
                
                // No delay between lookups to simulate real vehicle creation scenario
            }

            // Assert and log rapid lookup performance
            var avgRapidTime = rapidLookupMeasurements.Average(m => m.ElapsedTime.TotalMilliseconds);
            var maxRapidTime = rapidLookupMeasurements.Max(m => m.ElapsedTime.TotalMilliseconds);

            TestContext.WriteLine($"=== POST-OPTIMIZATION VEHICLE CREATION SCENARIO ===");
            TestContext.WriteLine($"Rapid Lookups: {rapidLookupMeasurements.Count}");
            TestContext.WriteLine($"Average Rapid Lookup Time: {avgRapidTime:F2} ms");
            TestContext.WriteLine($"Max Rapid Lookup Time: {maxRapidTime:F2} ms");
            
            foreach (var measurement in rapidLookupMeasurements)
            {
                TestContext.WriteLine($"  {measurement.TestScenario}: {measurement.ElapsedTime.TotalMilliseconds:F2} ms ({measurement.ResultCount} results)");
            }

            // Performance assertion - should be fast enough for real-time vehicle creation
            Assert.That(avgRapidTime, Is.LessThan(500), "Average lookup time should be under 500ms for vehicle creation scenarios");
        }

        private async Task CreateLargeTestDataSetAsync()
        {
            // Create identical dataset to pre-optimization tests for fair comparison
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Id = Guid.NewGuid();
            country.Name = "Australia";
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Id = Guid.NewGuid();
            region.Name = "Victoria";
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create multiple dealers for testing
            var dealers = new List<DealerDataObject>();
            for (int d = 0; d < 3; d++)
            {
                var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
                dealer.Id = Guid.NewGuid();
                dealer.Name = $"Test Dealer {d + 1}";
                dealer.RegionId = region.Id;
                dealer.Active = true;
                dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);
                dealers.Add(dealer);
            }

            // Create customers, sites, departments, and modules for each dealer
            foreach (var dealer in dealers)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = Guid.NewGuid();
                customer.CompanyName = $"Test Customer for {dealer.Name}";
                customer.CountryId = country.Id;
                customer.DealerId = dealer.Id;
                customer.Active = true;
                customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

                var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
                timeZone.Id = Guid.NewGuid();
                timeZone.TimezoneName = "AEST";
                timeZone.UTCOffset = 10;
                timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

                // Create multiple sites per customer
                for (int s = 0; s < 2; s++)
                {
                    var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                    site.Id = Guid.NewGuid();
                    site.CustomerId = customer.Id;
                    site.Name = $"Site {s + 1} - {dealer.Name}";
                    site.TimezoneId = timeZone.Id;
                    site = await _dataFacade.SiteDataProvider.SaveAsync(site);

                    // Create departments per site
                    for (int dept = 0; dept < 2; dept++)
                    {
                        var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                        department.Id = Guid.NewGuid();
                        department.Name = $"Department {dept + 1} - {site.Name}";
                        department.SiteId = site.Id;
                        department.CustomerId = customer.Id;
                        department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                        // Create models
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = $"Model {dept + 1}";
                        model.Description = $"Test Model {dept + 1}";
                        model.DealerId = dealer.Id;
                        model.Type = ModelTypesEnum.Electric;
                        model = await _dataFacade.ModelDataProvider.SaveAsync(model);

                        // Create many modules per department (50 modules, 40 assigned to vehicles, 10 spare)
                        for (int m = 0; m < 50; m++)
                        {
                            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                            module.Id = Guid.NewGuid();
                            module.Calibration = 100;
                            module.CCID = $"CCID-{dealer.Name}-{s}-{dept}-{m}";
                            module.IoTDevice = $"device_{dealer.Id}_{s}_{dept}_{m:D3}";
                            module.DealerId = dealer.Id;
                            module.Status = ModuleStatusEnum.Spare;
                            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

                            // Assign 40 out of 50 modules to vehicles (leaving 10 spare per department)
                            if (m < 40)
                            {
                                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                                vehicle.Id = Guid.NewGuid();
                                vehicle.CustomerId = customer.Id;
                                vehicle.SiteId = site.Id;
                                vehicle.DepartmentId = department.Id;
                                vehicle.ModelId = model.Id;
                                vehicle.HireNo = $"VH-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.SerialNo = $"VS-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.ModuleId1 = module.Id;
                                vehicle.OnHire = true;
                                vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                            }
                        }
                    }
                }
            }
        }
    }
}
