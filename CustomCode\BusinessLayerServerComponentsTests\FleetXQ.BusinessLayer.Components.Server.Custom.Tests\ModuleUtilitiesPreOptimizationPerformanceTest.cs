using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    /// <summary>
    /// Pre-Optimization Performance Test Suite for Device ID Lookup Operations
    /// 
    /// This test suite uses the original (unoptimized) implementation of GetAvailableModulesAsync
    /// to establish baseline performance metrics for device lookup operations during vehicle creation.
    /// 
    /// Original Implementation Details:
    /// - Loads all vehicles with modules to find used module IDs
    /// - Uses HashSet.Contains for module filtering
    /// - Performs separate vehicle and module queries
    /// - More complex query logic with multiple steps
    /// </summary>
    [TestFixture]
    public class ModuleUtilitiesPreOptimizationPerformanceTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"ModuleUtilitiesPreOptTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();

            CreateTestDatabase(_testDatabaseName);
            await CreateLargeTestDataSetAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        /// <summary>
        /// Performance measurement utility for consistent benchmarking
        /// </summary>
        public class PerformanceMeasurement
        {
            public TimeSpan ElapsedTime { get; set; }
            public int ResultCount { get; set; }
            public string TestScenario { get; set; }
            public Dictionary<string, object> AdditionalMetrics { get; set; } = new Dictionary<string, object>();
        }

        /// <summary>
        /// Original (Pre-Optimization) implementation of GetAvailableModulesAsync
        /// This replicates the logic from commit 3e07cd7ee0 before the optimization
        /// </summary>
        private async Task<DataObjectCollection<ModuleDataObject>> GetAvailableModulesAsync_PreOptimization(Guid dealerId)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Optimized approach: Load only module IDs from vehicles (not full objects)
                // This significantly reduces memory usage vs loading full vehicle objects
                var vehicleStopwatch = Stopwatch.StartNew();

                var vehicleFilter = "ModuleId1 != null";
                var vehicleParameters = new object[] { };

                // Add dealerId filter to vehicles if not empty
                if (dealerId != Guid.Empty)
                {
                    vehicleFilter += " && Customer.DealerId == @0";
                    vehicleParameters = new object[] { dealerId };
                }

                // Load vehicles but only select ModuleId1 to minimize data transfer
                var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, vehicleFilter, vehicleParameters);
                var usedModuleIds = vehiclesWithModules.Where(v => v.ModuleId1 != Guid.Empty)
                    .Select(v => v.ModuleId1)
                    .Distinct()
                    .ToHashSet(); // Use HashSet for O(1) lookup

                vehicleStopwatch.Stop();

                // Build the module query filter - use HashSet.Contains for better performance than LINQ Any()
                var moduleStopwatch = Stopwatch.StartNew();
                var queryFilter = "(Status == @0 || Status == null) && !(@1.Contains(outerIt.Id))";
                var queryParameters = new object[] { (int)ModuleStatusEnum.Spare, usedModuleIds };

                // Add dealerId filter for modules if not empty
                if (dealerId != Guid.Empty)
                {
                    queryFilter += " && DealerId == @2";
                    queryParameters = new object[] { (int)ModuleStatusEnum.Spare, usedModuleIds, dealerId };
                }

                var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, queryFilter, queryParameters);

                moduleStopwatch.Stop();
                stopwatch.Stop();

                // Log performance metrics
                var resultCount = result?.Count ?? 0;
                var usedModuleCount = usedModuleIds.Count;
                var vehicleCount = vehiclesWithModules?.Count() ?? 0;

                if (stopwatch.ElapsedMilliseconds > 1000) // Log slow queries
                {
                    Debug.WriteLine($"[PERF] GetAvailableModulesAsync_PreOptimization took {stopwatch.ElapsedMilliseconds}ms total " +
                        $"(vehicles: {vehicleStopwatch.ElapsedMilliseconds}ms, modules: {moduleStopwatch.ElapsedMilliseconds}ms), " +
                        $"processed {vehicleCount} vehicles with {usedModuleCount} used modules, returned {resultCount} available modules (DealerId: {dealerId})");
                }

                return new DataObjectCollection<ModuleDataObject>(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[PERF] GetAvailableModulesAsync_PreOptimization failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Measures the performance of a function execution
        /// </summary>
        private async Task<PerformanceMeasurement> MeasurePerformanceAsync<T>(
            Func<Task<T>> operation,
            string scenario,
            Func<T, int> resultCounter = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = await operation();
            stopwatch.Stop();

            return new PerformanceMeasurement
            {
                ElapsedTime = stopwatch.Elapsed,
                ResultCount = resultCounter?.Invoke(result) ?? 0,
                TestScenario = scenario
            };
        }

        /// <summary>
        /// Runs multiple iterations of a performance test and returns average metrics
        /// </summary>
        private async Task<PerformanceMeasurement> RunPerformanceTestAsync<T>(
            Func<Task<T>> operation,
            string scenario,
            int iterations = 5,
            Func<T, int> resultCounter = null)
        {
            var measurements = new List<PerformanceMeasurement>();

            // Warm-up run
            await operation();

            // Actual measurements
            for (int i = 0; i < iterations; i++)
            {
                var measurement = await MeasurePerformanceAsync(operation, scenario, resultCounter);
                measurements.Add(measurement);

                // Small delay between iterations to avoid caching effects
                await Task.Delay(100);
            }

            var avgElapsed = TimeSpan.FromMilliseconds(measurements.Average(m => m.ElapsedTime.TotalMilliseconds));
            var avgResultCount = (int)measurements.Average(m => m.ResultCount);

            return new PerformanceMeasurement
            {
                ElapsedTime = avgElapsed,
                ResultCount = avgResultCount,
                TestScenario = scenario,
                AdditionalMetrics = new Dictionary<string, object>
                {
                    ["MinTime"] = measurements.Min(m => m.ElapsedTime),
                    ["MaxTime"] = measurements.Max(m => m.ElapsedTime),
                    ["Iterations"] = iterations,
                    ["StandardDeviation"] = CalculateStandardDeviation(measurements.Select(m => m.ElapsedTime.TotalMilliseconds))
                }
            };
        }

        private double CalculateStandardDeviation(IEnumerable<double> values)
        {
            var avg = values.Average();
            var sumOfSquares = values.Sum(v => Math.Pow(v - avg, 2));
            return Math.Sqrt(sumOfSquares / values.Count());
        }

        [Test]
        public async Task PreOptimization_SingleDeviceLookup_PerformanceBaseline()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - Measure performance of single device lookup
            var measurement = await RunPerformanceTestAsync(
                () => GetAvailableModulesAsync_PreOptimization(dealer.Id),
                "Pre-Optimization Single Device Lookup",
                iterations: 10,
                resultCounter: result => result.Count);

            // Assert
            Assert.That(measurement.ResultCount, Is.GreaterThan(0), "Should return available modules");

            // Log baseline performance metrics
            TestContext.WriteLine($"=== PRE-OPTIMIZATION BASELINE METRICS ===");
            TestContext.WriteLine($"Scenario: {measurement.TestScenario}");
            TestContext.WriteLine($"Average Execution Time: {measurement.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Min Time: {((TimeSpan)measurement.AdditionalMetrics["MinTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Max Time: {((TimeSpan)measurement.AdditionalMetrics["MaxTime"]).TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Standard Deviation: {(double)measurement.AdditionalMetrics["StandardDeviation"]:F2} ms");
            TestContext.WriteLine($"Result Count: {measurement.ResultCount}");
            TestContext.WriteLine($"Iterations: {measurement.AdditionalMetrics["Iterations"]}");
        }

        [Test]
        public async Task PreOptimization_BatchDeviceLookup_PerformanceBaseline()
        {
            // Arrange
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(null);
            var testDealers = dealers.Take(3).ToList();
            Assert.That(testDealers.Count, Is.GreaterThan(0), "Test setup error: No dealers found.");

            // Act - Measure performance of batch device lookups
            var batchMeasurements = new List<PerformanceMeasurement>();

            foreach (var dealer in testDealers)
            {
                var measurement = await RunPerformanceTestAsync(
                    () => GetAvailableModulesAsync_PreOptimization(dealer.Id),
                    $"Pre-Optimization Batch Lookup - Dealer {dealer.Name}",
                    iterations: 5,
                    resultCounter: result => result.Count);

                batchMeasurements.Add(measurement);
            }

            // Assert and log batch performance
            var avgBatchTime = batchMeasurements.Average(m => m.ElapsedTime.TotalMilliseconds);
            var totalResults = batchMeasurements.Sum(m => m.ResultCount);

            TestContext.WriteLine($"=== PRE-OPTIMIZATION BATCH BASELINE METRICS ===");
            TestContext.WriteLine($"Dealers Tested: {testDealers.Count}");
            TestContext.WriteLine($"Average Batch Time: {avgBatchTime:F2} ms");
            TestContext.WriteLine($"Total Results: {totalResults}");

            foreach (var measurement in batchMeasurements)
            {
                TestContext.WriteLine($"  {measurement.TestScenario}: {measurement.ElapsedTime.TotalMilliseconds:F2} ms ({measurement.ResultCount} results)");
            }
        }

        private async Task CreateLargeTestDataSetAsync()
        {
            // Create a larger dataset to better demonstrate performance differences
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Id = Guid.NewGuid();
            country.Name = "Australia";
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Id = Guid.NewGuid();
            region.Name = "Victoria";
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create multiple dealers for testing
            var dealers = new List<DealerDataObject>();
            for (int d = 0; d < 3; d++)
            {
                var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
                dealer.Id = Guid.NewGuid();
                dealer.Name = $"Test Dealer {d + 1}";
                dealer.RegionId = region.Id;
                dealer.Active = true;
                dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);
                dealers.Add(dealer);
            }

            // Create customers, sites, departments, and modules for each dealer
            foreach (var dealer in dealers)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = Guid.NewGuid();
                customer.CompanyName = $"Test Customer for {dealer.Name}";
                customer.CountryId = country.Id;
                customer.DealerId = dealer.Id;
                customer.Active = true;
                customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

                var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
                timeZone.Id = Guid.NewGuid();
                timeZone.TimezoneName = "AEST";
                timeZone.UTCOffset = 10;
                timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

                // Create multiple sites per customer
                for (int s = 0; s < 2; s++)
                {
                    var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                    site.Id = Guid.NewGuid();
                    site.CustomerId = customer.Id;
                    site.Name = $"Site {s + 1} - {dealer.Name}";
                    site.TimezoneId = timeZone.Id;
                    site = await _dataFacade.SiteDataProvider.SaveAsync(site);

                    // Create departments per site
                    for (int dept = 0; dept < 2; dept++)
                    {
                        var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                        department.Id = Guid.NewGuid();
                        department.Name = $"Department {dept + 1} - {site.Name}";
                        department.SiteId = site.Id;
                        department.CustomerId = customer.Id;
                        department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                        // Create models
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = $"Model {dept + 1}";
                        model.Description = $"Test Model {dept + 1}";
                        model.DealerId = dealer.Id;
                        model.Type = ModelTypesEnum.Electric;
                        model = await _dataFacade.ModelDataProvider.SaveAsync(model);

                        // Create many modules per department (50 modules, 40 assigned to vehicles, 10 spare)
                        for (int m = 0; m < 50; m++)
                        {
                            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                            module.Id = Guid.NewGuid();
                            module.Calibration = 100;
                            module.CCID = $"CCID-{dealer.Name}-{s}-{dept}-{m}";
                            module.IoTDevice = $"device_{dealer.Id}_{s}_{dept}_{m:D3}";
                            module.DealerId = dealer.Id;
                            module.Status = ModuleStatusEnum.Spare;
                            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

                            // Assign 40 out of 50 modules to vehicles (leaving 10 spare per department)
                            if (m < 40)
                            {
                                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                                vehicle.Id = Guid.NewGuid();
                                vehicle.CustomerId = customer.Id;
                                vehicle.SiteId = site.Id;
                                vehicle.DepartmentId = department.Id;
                                vehicle.ModelId = model.Id;
                                vehicle.HireNo = $"VH-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.SerialNo = $"VS-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.ModuleId1 = module.Id;
                                vehicle.OnHire = true;
                                vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                            }
                        }
                    }
                }
            }
        }
    }
}
