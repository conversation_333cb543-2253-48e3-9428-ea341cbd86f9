# CSV Export/Import Translation Testing Plan
## LocaleTranslator Application - QA Testing Guide

### Overview
This document provides a step-by-step testing guide for the CSV export/import translation feature in the LocaleTranslator application. This guide is designed for QA testers who will be working with CSV files to review and update translations.

---

## Workflow Summary

The translation testing process follows these main steps:
1. **Developer** exports current translations to a CSV file
2. **QA Team** receives the CSV file for review
3. **QA Team** updates translations in the CSV file using Excel or similar software
4. **QA Team** sends the updated CSV file back to the developer
5. **Developer** imports the updated CSV file back into the application
6. **QA Team** verifies that changes were applied correctly

---

## Part 1: Understanding the CSV File

### What You'll Receive
You will receive a CSV file with a name like:
- `translations_french_20250115_143022.csv`
- `translations_spanish_20250115_143022.csv`

The filename tells you:
- **translations** = This is a translation file
- **french/spanish** = The target language
- **20250115_143022** = Date and time when it was created (YYYYMMDD_HHMMSS)

### CSV File Structure
When you open the CSV file in Excel or similar software, you'll see these columns:

| Column | Description | Example |
|--------|-------------|---------|
| **SourceText** | Original English text | "Save" |
| **TranslatedText** | Current translation | "Sauvegarder" |
| **SourceLanguage** | Always "english" | "english" |
| **TargetLanguage** | Language being translated to | "French" |
| **FilePath** | Where this text appears in the app | "Common.json" |
| **KeyPath** | Specific location within the file | "buttons.save" |

### What Each Column Means for Testing

- **SourceText**: The original English text - DO NOT CHANGE this column
- **TranslatedText**: This is what you need to review and update
- **SourceLanguage**: Tells you the source language - DO NOT CHANGE
- **TargetLanguage**: Tells you what language you're translating to - DO NOT CHANGE
- **FilePath**: Helps you understand where this text appears (forms, buttons, etc.)
- **KeyPath**: Helps you understand what type of text it is (button, label, message, etc.)

---

## Part 2: Review and Update Process

### Step 1: Open the CSV File
1. Open the CSV file in **Microsoft Excel** or **Google Sheets**
2. Make sure all text displays correctly (no strange characters)
3. If you see strange characters, save the file as "CSV UTF-8" format

### Step 2: Review Strategy
Start by understanding the content:

1. **Sort by FilePath** to group related translations together
2. **Sort by KeyPath** to see similar types of text (all buttons, all labels, etc.)
3. **Filter by specific text types** you want to focus on

### Step 3: What to Look For

#### ✅ Good Translations Should:
- Sound natural in the target language
- Use appropriate business/professional terminology
- Be consistent with similar text in the same context
- Maintain the same meaning as the English text
- Be appropriate for the user interface context

#### ❌ Problems to Watch For:
- Text that sounds too literal or awkward
- Inconsistent terminology (same English word translated differently)
- Text that's too long for button labels
- Missing or incorrect special characters (accents, umlauts, etc.)
- Technical terms that should remain in English
- Placeholders like {{name}} that were accidentally translated

### Step 4: Making Changes
1. **Only edit the TranslatedText column**
2. **Keep the same row** - don't add or delete rows
3. **Preserve any text in double curly braces** like {{username}} or {{date}}
4. **Keep special formatting** like quotation marks or punctuation when appropriate

### Step 5: Common Translation Types

#### Button Labels
- Should be short and action-oriented
- Examples: "Save", "Cancel", "Delete", "Edit"
- Look for these in KeyPath containing "buttons"

#### Form Labels
- Should be clear field descriptions
- Examples: "First Name", "Email Address", "Phone Number"
- Look for these in KeyPath containing field names

#### Validation Messages
- Should be helpful and professional
- Examples: "This field is required", "Invalid email format"
- Look for these in KeyPath containing "validation" or "error"

#### Menu Items
- Should be clear navigation terms
- Examples: "Dashboard", "Reports", "Settings"
- Look for these in KeyPath containing "navigation" or "menu"

---

## Part 3: Testing Scenarios

### Scenario 1: Basic Text Review
**Purpose**: Verify standard translations are natural and appropriate

**Steps**:
1. Open the CSV file in Excel
2. Sort by KeyPath column
3. Review all entries containing "buttons" in the KeyPath
4. Check that button text is short, clear, and action-oriented
5. Update any translations that sound awkward or too long

**What to Verify**:
- Button text fits typical button size (usually 1-3 words)
- Uses imperative form (command language)
- Consistent across all buttons

### Scenario 2: Form Field Labels
**Purpose**: Ensure form labels are user-friendly

**Steps**:
1. Filter or sort to find entries with KeyPath containing field names
2. Look for patterns like "firstName", "emailAddress", "phoneNumber"
3. Verify translations are appropriate for form labels
4. Check for consistency in similar field types

**What to Verify**:
- Field labels are clear and descriptive
- Professional terminology appropriate for business users
- Consistent formatting (Title Case vs. sentence case)

### Scenario 3: Error and Validation Messages
**Purpose**: Ensure error messages are helpful and professional

**Steps**:
1. Find entries with KeyPath containing "validation", "error", or "message"
2. Review that error messages are helpful, not confusing
3. Check that they guide users toward the correct action

**What to Verify**:
- Messages explain what went wrong
- Messages suggest how to fix the problem
- Professional, helpful tone (not accusatory)

### Scenario 4: Placeholder Preservation
**Purpose**: Ensure dynamic content placeholders are not translated

**Steps**:
1. Search for entries containing {{}} in the SourceText
2. Verify the TranslatedText still contains the exact same {{}} placeholders
3. Check that only the surrounding text is translated

**Example**:
- ✅ Correct: "Hello {{username}}" → "Bonjour {{username}}"
- ❌ Wrong: "Hello {{username}}" → "Bonjour {{nom_utilisateur}}"

### Scenario 5: Technical Terms
**Purpose**: Verify technical terms are handled appropriately

**Steps**:
1. Look for technical terms in SourceText (API, URL, email, etc.)
2. Decide if the term should be translated or kept in English
3. Check for consistency across all instances

**Guidelines**:
- Keep widely-used technical terms in English: API, URL, email, login
- Translate general business terms: report, dashboard, user, account
- When in doubt, ask the development team

### Scenario 6: Long Text Review
**Purpose**: Ensure longer text blocks are well-translated

**Steps**:
1. Sort by length of SourceText (longest first)
2. Focus on entries with multiple sentences or long descriptions
3. Verify natural flow and readability

**What to Verify**:
- Sentences flow naturally in the target language
- Maintains professional business tone
- Cultural appropriateness for the target audience

---

## Part 4: Quality Verification Steps

### After Making Changes

#### Step 1: Basic Validation
1. **Count rows**: Ensure you have the same number of rows as when you started
2. **Check columns**: Verify you only changed the TranslatedText column
3. **Save properly**: Save as CSV UTF-8 format to preserve special characters

#### Step 2: Consistency Check
1. **Search for repeated English terms** to ensure consistent translation
2. **Check similar contexts** (all buttons, all error messages) for consistency
3. **Verify placeholder preservation** - search for {{ to find all placeholders

#### Step 3: Final Review
1. **Spot check 10-20 random entries** for quality
2. **Review any entries you're unsure about**
3. **Double-check any technical terms** you decided to translate or keep

### Before Sending Back

#### File Naming
- **Keep the original filename** or add "_reviewed" to the end
- Example: `translations_french_20250115_143022_reviewed.csv`

#### Delivery Notes
Include a brief note with:
- Number of changes made
- Any questions or concerns about specific translations
- Any technical terms you weren't sure about
- Any very long text that might not fit in the interface

---

## Part 5: Common Issues and Solutions

### Issue 1: Strange Characters or Symbols
**Problem**: Text shows as squares, question marks, or strange symbols
**Solution**: 
- Save the file as "CSV UTF-8" in Excel
- Or open in Google Sheets which handles UTF-8 automatically

### Issue 2: Text Too Long for Interface
**Problem**: Translated text much longer than English original
**Solution**:
- Try to find shorter, equivalent phrases
- Focus on button labels and short field names
- Note concerns in your delivery feedback

### Issue 3: Inconsistent Terminology
**Problem**: Same English word translated differently in different places
**Solution**:
- Use Excel's Find & Replace to standardize terminology
- Create a simple terminology list for consistency
- Search for the English term to find all instances

### Issue 4: Technical Terms Confusion
**Problem**: Unsure whether to translate technical terms
**Solution**:
- Generally keep well-known tech terms in English (email, login, API)
- Translate business terms (report, dashboard, user)
- When in doubt, ask the development team or leave unchanged

### Issue 5: Cultural Appropriateness
**Problem**: Translation is correct but doesn't feel right for the culture
**Solution**:
- Consider local business customs and language preferences
- Ask native speakers in your organization for input
- Note cultural concerns in your feedback

---

## Part 6: Verification After Import

### What the Developer Will Do
1. Import your updated CSV file back into the application
2. Rebuild the application with new translations
3. Ask you to verify the changes in the running application

### Your Verification Tasks

#### Step 1: Spot Check Interface
1. **Navigate through the application** looking for your changes
2. **Check button labels** to ensure they fit properly
3. **Verify form labels** appear correctly
4. **Test error messages** by triggering validation errors

#### Step 2: Functionality Testing
1. **Test that buttons still work** after translation
2. **Verify forms still function** with new labels
3. **Check that navigation still works** with translated menu items

#### Step 3: Visual Layout Check
1. **Look for text overflow** - text that extends beyond its container
2. **Check button sizing** - buttons that became too wide or narrow
3. **Verify alignment** - text that no longer lines up properly
4. **Test responsive design** - how translations look on different screen sizes

#### Step 4: Report Results
Document any issues you find:
- **Text overflow**: "The 'Save Changes' button is too wide on the user form"
- **Missing translations**: "The error message still appears in English"
- **Visual problems**: "The navigation menu items don't align properly"

---

## Part 7: Sample Test Cases

### Test Case 1: Button Translation Verification
**Scenario**: Verify all button translations are appropriate and functional

**Steps**:
1. Open CSV and filter for KeyPath containing "buttons"
2. Review each translation for appropriateness and length
3. Update any problematic translations
4. After import, click each button to verify functionality
5. Check that buttons fit properly in their containers

**Expected Result**: All buttons display correctly and function normally

### Test Case 2: Form Validation Testing
**Scenario**: Ensure validation messages are clear and helpful

**Steps**:
1. Review all validation-related translations in CSV
2. Update messages to be helpful and professional
3. After import, test forms by:
   - Leaving required fields empty
   - Entering invalid data (wrong email format, etc.)
   - Exceeding field length limits
4. Verify error messages appear in the target language and make sense

**Expected Result**: Error messages appear in target language and guide users effectively

### Test Case 3: Navigation and Menu Testing
**Scenario**: Verify navigation elements are properly translated

**Steps**:
1. Review navigation-related translations in CSV
2. Update menu items and navigation labels
3. After import, test:
   - Main navigation menu
   - Breadcrumb navigation
   - Page titles
   - Tab labels
4. Verify all navigation still works correctly

**Expected Result**: Navigation displays in target language and functions normally

### Test Case 4: Data Display Testing
**Scenario**: Check that data labels and headers are properly translated

**Steps**:
1. Review column headers, labels, and data display text
2. Update translations for clarity and consistency
3. After import, test:
   - Data tables and grids
   - Report headers and labels
   - Dashboard widgets
   - Data entry forms

**Expected Result**: Data displays with proper translated labels while maintaining functionality

---

## Part 8: Reporting Issues

### When to Report an Issue
- Translation appears incorrect in the application
- Text doesn't fit properly in the interface
- Functionality breaks after translation update
- Missing translations (still showing English)
- Cultural or professional appropriateness concerns

### How to Report Issues

#### For Translation Problems
```
Issue Type: Translation Error
Location: User Management > Edit User Form > Save Button
Problem: Button text "Sauvegarder les modifications" is too long and gets cut off
Suggested Fix: Use shorter text "Sauvegarder" instead
```

#### For Technical Problems
```
Issue Type: Functionality Issue
Location: Login Form > Email Validation
Problem: Error message still appears in English when entering invalid email
Expected: Should show French error message "Format d'email invalide"
```

#### For Visual Problems
```
Issue Type: Layout Issue
Location: Dashboard > Navigation Menu
Problem: Translated menu items cause navigation bar to wrap to two lines
Impact: Makes interface look unprofessional
```

### Include This Information
1. **Exact location** where the problem appears
2. **What you expected** to see
3. **What actually happened**
4. **Screenshots** if visual issues
5. **Steps to reproduce** the problem

---

## Part 9: Best Practices Summary

### Do's ✅
- **Test systematically** using the provided scenarios
- **Document all changes** you make
- **Ask questions** when you're unsure about technical terms
- **Consider cultural context** for your target audience
- **Verify functionality** after translations are imported
- **Use consistent terminology** throughout the application
- **Keep placeholder text** like {{username}} unchanged
- **Save files in UTF-8 format** to preserve special characters

### Don'ts ❌
- **Don't change source English text**
- **Don't modify file structure or add/remove rows**
- **Don't translate technical terms** without consulting the team
- **Don't ignore text overflow** problems
- **Don't assume translations are correct** without testing
- **Don't translate placeholder variables** in {{}}
- **Don't make changes** to columns other than TranslatedText

### Quality Checklist
Before submitting your reviewed CSV file:

- [ ] All translations sound natural in the target language
- [ ] Technical terms are handled appropriately
- [ ] Placeholder text {{}} is preserved exactly
- [ ] Consistent terminology is used throughout
- [ ] Button labels are appropriately short
- [ ] Error messages are helpful and professional
- [ ] File is saved in proper CSV UTF-8 format
- [ ] Row count matches original file
- [ ] Only TranslatedText column was modified

---

## Contact Information

### For Translation Questions
- Contact the localization team lead
- Consult with native speakers in your organization
- Reference existing company style guides for the target language

### For Technical Issues
- Contact the development team for import/export problems
- Report functionality issues through normal bug reporting channels
- Include detailed steps to reproduce any technical problems

### For Process Questions
- Refer back to this testing plan
- Contact the QA team lead for clarification
- Document any process improvements for future reference

---

*This testing plan ensures high-quality translations while maintaining application functionality and professional appearance.*

