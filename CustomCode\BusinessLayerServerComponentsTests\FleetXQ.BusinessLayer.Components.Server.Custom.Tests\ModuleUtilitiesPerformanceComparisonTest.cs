using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    /// <summary>
    /// Performance Comparison Test Suite - Device Speed Optimization Proof
    /// 
    /// This test suite demonstrates the quantified performance improvements achieved by the device speed 
    /// optimization work by running both pre-optimization and post-optimization implementations side by side
    /// using identical test data and measurement methodology.
    /// 
    /// This serves as concrete proof of the optimization work completed and provides specific metrics
    /// showing the performance gains achieved.
    /// </summary>
    [TestFixture]
    public class ModuleUtilitiesPerformanceComparisonTest : TestBase
    {
        private IModuleUtilities _moduleUtilities;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"ModuleUtilitiesComparisonTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _moduleUtilities = _serviceProvider.GetRequiredService<IModuleUtilities>();

            CreateTestDatabase(_testDatabaseName);
            await CreateLargeTestDataSetAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        /// <summary>
        /// Performance measurement utility for consistent benchmarking
        /// </summary>
        public class PerformanceMeasurement
        {
            public TimeSpan ElapsedTime { get; set; }
            public int ResultCount { get; set; }
            public string TestScenario { get; set; }
            public Dictionary<string, object> AdditionalMetrics { get; set; } = new Dictionary<string, object>();
        }

        /// <summary>
        /// Performance comparison result showing before/after metrics
        /// </summary>
        public class PerformanceComparison
        {
            public PerformanceMeasurement PreOptimization { get; set; }
            public PerformanceMeasurement PostOptimization { get; set; }
            public double ImprovementPercentage { get; set; }
            public TimeSpan TimeSaved { get; set; }
            public string Summary { get; set; }
        }

        /// <summary>
        /// Original (Pre-Optimization) implementation of GetAvailableModulesAsync
        /// This replicates the logic from commit 3e07cd7ee0 before the optimization
        /// </summary>
        private async Task<DataObjectCollection<ModuleDataObject>> GetAvailableModulesAsync_PreOptimization(Guid dealerId)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Original complex approach: Load vehicles first, then filter modules
                var vehicleFilter = "ModuleId1 != null";
                var vehicleParameters = new object[] { };

                if (dealerId != Guid.Empty)
                {
                    vehicleFilter += " && Customer.DealerId == @0";
                    vehicleParameters = new object[] { dealerId };
                }

                var vehiclesWithModules = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, vehicleFilter, vehicleParameters);
                var usedModuleIds = vehiclesWithModules.Where(v => v.ModuleId1 != Guid.Empty)
                    .Select(v => v.ModuleId1)
                    .Distinct()
                    .ToHashSet();

                var queryFilter = "(Status == @0 || Status == null) && !(@1.Contains(outerIt.Id))";
                var queryParameters = new object[] { (int)ModuleStatusEnum.Spare, usedModuleIds };

                if (dealerId != Guid.Empty)
                {
                    queryFilter += " && DealerId == @2";
                    queryParameters = new object[] { (int)ModuleStatusEnum.Spare, usedModuleIds, dealerId };
                }

                var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, queryFilter, queryParameters);
                return new DataObjectCollection<ModuleDataObject>(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[PERF] GetAvailableModulesAsync_PreOptimization failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Measures the performance of a function execution
        /// </summary>
        private async Task<PerformanceMeasurement> MeasurePerformanceAsync<T>(
            Func<Task<T>> operation,
            string scenario,
            Func<T, int> resultCounter = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = await operation();
            stopwatch.Stop();

            return new PerformanceMeasurement
            {
                ElapsedTime = stopwatch.Elapsed,
                ResultCount = resultCounter?.Invoke(result) ?? 0,
                TestScenario = scenario
            };
        }

        /// <summary>
        /// Runs multiple iterations and compares pre vs post optimization performance
        /// </summary>
        private async Task<PerformanceComparison> ComparePerformanceAsync(
            Func<Task<DataObjectCollection<ModuleDataObject>>> preOptOperation,
            Func<Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>>> postOptOperation,
            string scenario,
            int iterations = 5)
        {
            var preMeasurements = new List<PerformanceMeasurement>();
            var postMeasurements = new List<PerformanceMeasurement>();

            // Warm-up runs
            await preOptOperation();
            await postOptOperation();

            // Measure pre-optimization performance
            for (int i = 0; i < iterations; i++)
            {
                var measurement = await MeasurePerformanceAsync(
                    preOptOperation,
                    $"Pre-Optimization {scenario}",
                    result => result.Count);
                preMeasurements.Add(measurement);
                await Task.Delay(100);
            }

            // Measure post-optimization performance
            for (int i = 0; i < iterations; i++)
            {
                var measurement = await MeasurePerformanceAsync(
                    postOptOperation,
                    $"Post-Optimization {scenario}",
                    result => result.Result.Count);
                postMeasurements.Add(measurement);
                await Task.Delay(100);
            }

            var preAvg = preMeasurements.Average(m => m.ElapsedTime.TotalMilliseconds);
            var postAvg = postMeasurements.Average(m => m.ElapsedTime.TotalMilliseconds);
            var improvement = ((preAvg - postAvg) / preAvg) * 100;
            var timeSaved = TimeSpan.FromMilliseconds(preAvg - postAvg);

            return new PerformanceComparison
            {
                PreOptimization = new PerformanceMeasurement
                {
                    ElapsedTime = TimeSpan.FromMilliseconds(preAvg),
                    ResultCount = (int)preMeasurements.Average(m => m.ResultCount),
                    TestScenario = $"Pre-Optimization {scenario}",
                    AdditionalMetrics = new Dictionary<string, object>
                    {
                        ["MinTime"] = preMeasurements.Min(m => m.ElapsedTime),
                        ["MaxTime"] = preMeasurements.Max(m => m.ElapsedTime),
                        ["Iterations"] = iterations
                    }
                },
                PostOptimization = new PerformanceMeasurement
                {
                    ElapsedTime = TimeSpan.FromMilliseconds(postAvg),
                    ResultCount = (int)postMeasurements.Average(m => m.ResultCount),
                    TestScenario = $"Post-Optimization {scenario}",
                    AdditionalMetrics = new Dictionary<string, object>
                    {
                        ["MinTime"] = postMeasurements.Min(m => m.ElapsedTime),
                        ["MaxTime"] = postMeasurements.Max(m => m.ElapsedTime),
                        ["Iterations"] = iterations
                    }
                },
                ImprovementPercentage = improvement,
                TimeSaved = timeSaved,
                Summary = $"{improvement:F1}% faster ({timeSaved.TotalMilliseconds:F1}ms saved)"
            };
        }

        [Test]
        public async Task DeviceSpeedOptimization_SingleLookup_PerformanceComparison()
        {
            // Arrange
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - Compare pre vs post optimization performance
            var comparison = await ComparePerformanceAsync(
                () => GetAvailableModulesAsync_PreOptimization(dealer.Id),
                () => _moduleUtilities.GetAvailableModulesAsync(dealer.Id),
                "Single Device Lookup",
                iterations: 10);

            // Assert
            Assert.That(comparison.PreOptimization.ResultCount, Is.EqualTo(comparison.PostOptimization.ResultCount),
                "Both implementations should return the same number of results");
            Assert.That(comparison.ImprovementPercentage, Is.GreaterThan(0),
                "Post-optimization should be faster than pre-optimization");

            // Log detailed comparison results
            TestContext.WriteLine($"=== DEVICE SPEED OPTIMIZATION PROOF - SINGLE LOOKUP ===");
            TestContext.WriteLine($"Pre-Optimization Average Time: {comparison.PreOptimization.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Post-Optimization Average Time: {comparison.PostOptimization.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Performance Improvement: {comparison.Summary}");
            TestContext.WriteLine($"Results Returned: {comparison.PostOptimization.ResultCount}");
            TestContext.WriteLine($"Test Iterations: {comparison.PreOptimization.AdditionalMetrics["Iterations"]}");
        }

        [Test]
        public async Task DeviceSpeedOptimization_BatchLookup_PerformanceComparison()
        {
            // Arrange
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(null);
            var testDealers = dealers.Take(3).ToList();
            Assert.That(testDealers.Count, Is.GreaterThan(0), "Test setup error: No dealers found.");

            var batchComparisons = new List<PerformanceComparison>();

            // Act - Compare batch performance for multiple dealers
            foreach (var dealer in testDealers)
            {
                var comparison = await ComparePerformanceAsync(
                    () => GetAvailableModulesAsync_PreOptimization(dealer.Id),
                    () => _moduleUtilities.GetAvailableModulesAsync(dealer.Id),
                    $"Batch Lookup - {dealer.Name}",
                    iterations: 5);

                batchComparisons.Add(comparison);
            }

            // Assert and log batch comparison results
            var avgImprovement = batchComparisons.Average(c => c.ImprovementPercentage);
            var totalTimeSaved = TimeSpan.FromMilliseconds(batchComparisons.Sum(c => c.TimeSaved.TotalMilliseconds));

            Assert.That(avgImprovement, Is.GreaterThan(0), "Average improvement should be positive");

            TestContext.WriteLine($"=== DEVICE SPEED OPTIMIZATION PROOF - BATCH LOOKUP ===");
            TestContext.WriteLine($"Dealers Tested: {testDealers.Count}");
            TestContext.WriteLine($"Average Performance Improvement: {avgImprovement:F1}%");
            TestContext.WriteLine($"Total Time Saved: {totalTimeSaved.TotalMilliseconds:F1} ms");

            foreach (var comparison in batchComparisons)
            {
                TestContext.WriteLine($"  {comparison.PreOptimization.TestScenario}: {comparison.Summary}");
            }
        }

        [Test]
        public async Task DeviceSpeedOptimization_EmptyDealer_PerformanceComparison()
        {
            // Act - Compare performance for empty dealer ID (all modules)
            var comparison = await ComparePerformanceAsync(
                () => GetAvailableModulesAsync_PreOptimization(Guid.Empty),
                () => _moduleUtilities.GetAvailableModulesAsync(Guid.Empty),
                "Empty Dealer Lookup (All Modules)",
                iterations: 10);

            // Assert
            Assert.That(comparison.PreOptimization.ResultCount, Is.EqualTo(comparison.PostOptimization.ResultCount),
                "Both implementations should return the same number of results");
            Assert.That(comparison.ImprovementPercentage, Is.GreaterThan(0),
                "Post-optimization should be faster than pre-optimization");

            // Log comparison results
            TestContext.WriteLine($"=== DEVICE SPEED OPTIMIZATION PROOF - ALL MODULES LOOKUP ===");
            TestContext.WriteLine($"Pre-Optimization Average Time: {comparison.PreOptimization.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Post-Optimization Average Time: {comparison.PostOptimization.ElapsedTime.TotalMilliseconds:F2} ms");
            TestContext.WriteLine($"Performance Improvement: {comparison.Summary}");
            TestContext.WriteLine($"Total Available Modules: {comparison.PostOptimization.ResultCount}");
        }

        [Test]
        public async Task DeviceSpeedOptimization_VehicleCreationScenario_PerformanceComparison()
        {
            // Arrange - Simulate rapid lookups during vehicle creation
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test setup error: No dealer found.");

            // Act - Measure rapid successive lookups (simulating vehicle creation workflow)
            var rapidComparisons = new List<PerformanceComparison>();

            for (int i = 0; i < 5; i++)
            {
                var comparison = await ComparePerformanceAsync(
                    () => GetAvailableModulesAsync_PreOptimization(dealer.Id),
                    () => _moduleUtilities.GetAvailableModulesAsync(dealer.Id),
                    $"Vehicle Creation Simulation {i + 1}",
                    iterations: 3);

                rapidComparisons.Add(comparison);
            }

            // Assert and log vehicle creation scenario results
            var avgImprovement = rapidComparisons.Average(c => c.ImprovementPercentage);
            var avgPreTime = rapidComparisons.Average(c => c.PreOptimization.ElapsedTime.TotalMilliseconds);
            var avgPostTime = rapidComparisons.Average(c => c.PostOptimization.ElapsedTime.TotalMilliseconds);

            Assert.That(avgImprovement, Is.GreaterThan(0), "Vehicle creation scenario should show improvement");
            Assert.That(avgPostTime, Is.LessThan(500), "Post-optimization should be fast enough for real-time vehicle creation");

            TestContext.WriteLine($"=== DEVICE SPEED OPTIMIZATION PROOF - VEHICLE CREATION SCENARIO ===");
            TestContext.WriteLine($"Rapid Lookups Simulated: {rapidComparisons.Count}");
            TestContext.WriteLine($"Average Pre-Optimization Time: {avgPreTime:F2} ms");
            TestContext.WriteLine($"Average Post-Optimization Time: {avgPostTime:F2} ms");
            TestContext.WriteLine($"Average Performance Improvement: {avgImprovement:F1}%");
            TestContext.WriteLine($"Vehicle Creation Suitability: {(avgPostTime < 500 ? "EXCELLENT" : "NEEDS IMPROVEMENT")}");
        }

        private async Task CreateLargeTestDataSetAsync()
        {
            // Create substantial test dataset to demonstrate performance differences
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Id = Guid.NewGuid();
            country.Name = "Australia";
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Id = Guid.NewGuid();
            region.Name = "Victoria";
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create multiple dealers for comprehensive testing
            var dealers = new List<DealerDataObject>();
            for (int d = 0; d < 3; d++)
            {
                var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
                dealer.Id = Guid.NewGuid();
                dealer.Name = $"Test Dealer {d + 1}";
                dealer.RegionId = region.Id;
                dealer.Active = true;
                dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);
                dealers.Add(dealer);
            }

            // Create comprehensive test data for each dealer
            foreach (var dealer in dealers)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = Guid.NewGuid();
                customer.CompanyName = $"Test Customer for {dealer.Name}";
                customer.CountryId = country.Id;
                customer.DealerId = dealer.Id;
                customer.Active = true;
                customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

                var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
                timeZone.Id = Guid.NewGuid();
                timeZone.TimezoneName = "AEST";
                timeZone.UTCOffset = 10;
                timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

                // Create multiple sites per customer
                for (int s = 0; s < 2; s++)
                {
                    var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                    site.Id = Guid.NewGuid();
                    site.CustomerId = customer.Id;
                    site.Name = $"Site {s + 1} - {dealer.Name}";
                    site.TimezoneId = timeZone.Id;
                    site = await _dataFacade.SiteDataProvider.SaveAsync(site);

                    // Create departments per site
                    for (int dept = 0; dept < 2; dept++)
                    {
                        var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                        department.Id = Guid.NewGuid();
                        department.Name = $"Department {dept + 1} - {site.Name}";
                        department.SiteId = site.Id;
                        department.CustomerId = customer.Id;
                        department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                        // Create models
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = $"Model {dept + 1}";
                        model.Description = $"Test Model {dept + 1}";
                        model.DealerId = dealer.Id;
                        model.Type = ModelTypesEnum.Electric;
                        model = await _dataFacade.ModelDataProvider.SaveAsync(model);

                        // Create many modules per department (50 modules, 40 assigned to vehicles, 10 spare)
                        for (int m = 0; m < 50; m++)
                        {
                            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                            module.Id = Guid.NewGuid();
                            module.Calibration = 100;
                            module.CCID = $"CCID-{dealer.Name}-{s}-{dept}-{m}";
                            module.IoTDevice = $"device_{dealer.Id}_{s}_{dept}_{m:D3}";
                            module.DealerId = dealer.Id;
                            module.Status = ModuleStatusEnum.Spare;
                            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

                            // Assign 40 out of 50 modules to vehicles (leaving 10 spare per department)
                            if (m < 40)
                            {
                                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                                vehicle.Id = Guid.NewGuid();
                                vehicle.CustomerId = customer.Id;
                                vehicle.SiteId = site.Id;
                                vehicle.DepartmentId = department.Id;
                                vehicle.ModelId = model.Id;
                                vehicle.HireNo = $"VH-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.SerialNo = $"VS-{dealer.Name}-{s}-{dept}-{m}";
                                vehicle.ModuleId1 = module.Id;
                                vehicle.OnHire = true;
                                vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                            }
                        }
                    }
                }
            }
        }
    }
}
